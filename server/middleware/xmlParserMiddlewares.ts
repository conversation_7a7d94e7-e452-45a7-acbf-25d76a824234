import xmlparser from 'express-xml-bodyparser';

/**
 * XML notification request for customer journey field change trigger
 *
 <?xml version="1.0" encoding="UTF-8"?>
 <soapenv:Envelope
 xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
 xmlns:xsd="http://www.w3.org/2001/XMLSchema"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
 <soapenv:Body>
 <notifications
 xmlns="http://soap.sforce.com/2005/09/outbound">
 <OrganizationId>00D3z000001BYGGEA4</OrganizationId>
 <ActionId>04k3z000000GxOzAAK</ActionId>
 <SessionId xsi:nil="true"/>
 <EnterpriseUrl>https://worldwidegeneration.my.salesforce.com/services/Soap/c/52.0/00D3z000001BYGG</EnterpriseUrl>
 <PartnerUrl>https://worldwidegeneration.my.salesforce.com/services/Soap/u/52.0/00D3z000001BYGG</PartnerUrl>
 <Notification>
 <Id>04l3z00001a8PT1AAM</Id>
 <sObject xsi:type="sf:Account"
 xmlns:sf="urn:sobject.enterprise.soap.sforce.com">
 <sf:Id>0013z00002PUj2qAAD</sf:Id>
 <sf:Account_Status__c>Active</sf:Account_Status__c>
 <sf:Customer_Journey__c>Pipeline</sf:Customer_Journey__c>
 <sf:G17Eco_ID__c>5babc4e326140135c24b5ad0</sf:G17Eco_ID__c>
 </sObject>
 </Notification>
 </notifications>
 </soapenv:Body>
 </soapenv:Envelope>
 */

// Account Object Props
// Name
// AccountNumber
// Customer_Journey__c
// Account_Status__c
// Company_Registration__c
// Contract_type__c
// G17Eco_ID__c
// Industry

export const xmlParserHandler = xmlparser({
  trim: true,
  explicitArray: false,
  normalizeTags: false,
  ignoreAttrs: true,
});
