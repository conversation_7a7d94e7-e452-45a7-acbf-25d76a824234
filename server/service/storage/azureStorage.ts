/*
 * Copyright (c) 2024. World Wide Generation Ltd
 */

import { BlobServiceClient, ContainerClient, BlobSASPermissions, ContainerSASPermissions, StorageSharedKeyCredential } from '@azure/storage-blob';
import config from '../../config';
import { FileStorageInterface, DownloadFolderParams, StreamFileUploadParams, UploadFileParams, CopyFileParams, retryOperation } from './fileStorage';
import mime from 'mime-types';
import { PassThrough } from 'stream';
import { wwgLogger } from '../wwgLogger';
import ContextError from '../../error/ContextError';
import * as path from "path";
import fs from 'fs';
import { mkDirByPathSync } from '../file/filesystem';
import sanitize from 'sanitize-filename';

export class AzureStorage implements FileStorageInterface {
  private readonly containerClient: ContainerClient;
  private readonly baseUrl = config.azure.storage.baseUrl;

  static create() {
    const sharedKeyCredential = new StorageSharedKeyCredential(
      config.azure.storage.accountName,
      config.azure.storage.accountKey
    );
    const blobServiceClient = new BlobServiceClient(config.azure.storage.baseUrl, sharedKeyCredential);

    const containerClient = blobServiceClient.getContainerClient(config.azure.storage.containerName);
    wwgLogger.info(`Creating AzureStorage connection to ${config.azure.storage.containerName}`);
    return new AzureStorage(containerClient);
  }

  constructor(containerClient: ContainerClient) {
    this.containerClient = containerClient;
  }

  public async getChecks(): Promise<{ [key: string]: number | string }> {
    const status: { [key: string]: number | string } = {
      type: 'Azure',
    };

    try {
      await this.containerClient.listBlobsFlat().next();
      status[this.containerClient.containerName] = 'OK';
    } catch (error) {
      wwgLogger.error(new ContextError('AzureStorage credentials are invalid or an error occurred:', { cause: error }));
      status[this.containerClient.containerName] = 'FAIL';
      status.error = String(error.message);
    }

    return status;
  }

  public async upload(source: string, uploadPath: string, contentType: string) {
    const blockBlobClient = this.containerClient.getBlockBlobClient(this.getUploadPath(uploadPath));
    await blockBlobClient.uploadFile(source, {
      blobHTTPHeaders: { blobContentType: contentType },
    });
    return {
      url: `${this.baseUrl}/${this.containerClient.containerName}/${blockBlobClient.name}`,
      path: blockBlobClient.name,
    };
  }

  public async uploadFile({ source, destination, contentType }: UploadFileParams) {
    if (!fs.existsSync(source)) {
      throw new ContextError(`File not found`, { source });
    }

    const blockBlobClient = this.containerClient.getBlockBlobClient(destination);
    const options = contentType ? { blobHTTPHeaders: { blobContentType: contentType } } : {};

    const stream = fs.createReadStream(source);
    const bufferSize = 4 * 1024 * 1024; // 4MB
    const maxConcurrency = 10; // Work together with buffer size for uploading files (<50MB)

    // Track file size
    const fileSize = fs.statSync(source).size;
    const startTime = performance.now();

    let activeUploads = 0;
    try {
      // Wrap the uploadStream call with logging
      const uploadWithLogging = async () => {
        activeUploads++;
        const chunkStartTime = performance.now();

        try {
          if (activeUploads >= maxConcurrency) {
            wwgLogger.warn(`[BOTTLENECK] Active uploads: ${activeUploads} reached max concurrency: ${maxConcurrency}`);
          }

          await blockBlobClient.uploadStream(stream, bufferSize, maxConcurrency, options);
        } finally {
          activeUploads--;
          const chunkDuration = (performance.now() - chunkStartTime) / 1000;
          wwgLogger.info(`[INFO] Chunk uploaded in ${chunkDuration.toFixed(2)} seconds`);

          if (chunkDuration > 5) {
            wwgLogger.warn(`[BOTTLENECK] Chunk took too long (${chunkDuration.toFixed(2)}s). Possible network issue.`);
          }
        }
      };

      await uploadWithLogging();

      const totalDuration = (performance.now() - startTime) / 1000;
      wwgLogger.info(`[INFO] Total upload time: ${totalDuration.toFixed(2)} seconds`);

      // Calculates average speed (MB/s) at the end.
      // Warns if the speed is below 1MB/s, which could indicate network congestion.
      // Checking for totalDuration = 0, it means uploading process finishes right away.
      const avgSpeed = totalDuration !== 0 ? fileSize / totalDuration / (1024 * 1024) : 1;
      if (avgSpeed < 1) {
        wwgLogger.warn(
          `[BOTTLENECK] Upload speed is slow: ${avgSpeed.toFixed(2)} MB/s. Possible network or disk I/O issue.`
        );
      }

      return {
        url: `${this.baseUrl}/${this.containerClient.containerName}/${blockBlobClient.name}`,
        path: blockBlobClient.name,
      };
    } catch (error) {
      wwgLogger.error(`[ERROR] Upload failed: ${error.message}`);
      throw error;
    }
  }

  public getPublicPath() {
    return `${this.baseUrl}/${this.containerClient.containerName}`;
  }

  public getPublicUrl(filePath: string) {
    return `${this.getPublicPath()}/${this.getUploadPath(filePath)}`;
  }

  public getExtensionFromMimeType(mimeType: string): string {
    return String(mime.extension(mimeType));
  }

  public getUploadPath(destination: string) {
    if (typeof destination !== 'string') {
      throw new Error(`Expected destination to be type of string, received ${typeof destination}`);
    }

    const dirs = destination.split('/');
    const dest = dirs.pop();
    if (typeof dest !== 'string') {
      throw new Error(`Last Dir to be available`);
    }

    const chars = dest.split('');
    const count = chars.length > 3 ? 3 : chars.length;

    let prefix = '';
    for (let i = 0; i < count; i++) {
      prefix += dest.charAt(i) + '/';
    }

    const url = `${dirs.join('/')}/${prefix}${dest}`;
    return url.replace(/^\/+/g, '');
  }

  public async getDownloadUrl(path: string, fileName: string, options?: { pathType: 'exact' }): Promise<[string]> {
    const filePath = options && options.pathType === 'exact' ? path : this.getUploadPath(path);
    const blockBlobClient = this.containerClient.getBlockBlobClient(filePath);
    const sasUrl = await blockBlobClient.generateSasUrl({
      permissions: BlobSASPermissions.parse('r'),
      expiresOn: new Date(Date.now() + 3600 * 1000),
    });
    return [sasUrl];
  }

  public async downloadFile(path: string, destination: string): Promise<[Buffer]> {
    const blockBlobClient = this.containerClient.getBlockBlobClient(path);
    const downloadResponse = await blockBlobClient.download();

    // Accumulate the data chunks
    const chunks: Uint8Array[] = [];
    const readableStream = downloadResponse.readableStreamBody;
    if (!readableStream) {
      return [Buffer.concat(chunks)];
    }

    return new Promise((resolve, reject) => {
      readableStream.on('data', (chunk) => {
        chunks.push(chunk);
      });

      readableStream.on('end', () => {
        // Concatenate all chunks into a single Buffer
        const buffer = Buffer.concat(chunks);
        resolve([buffer]);
      });

      readableStream.on('error', (error) => {
        reject(error);
      });
    });
  }

  public async getSignedUrl(path: string, options?: {expireTimestamp?: number, fileName?: string }): Promise<string[]> {
    const blockBlobClient = this.containerClient.getBlockBlobClient(path);
    const sasUrl = await blockBlobClient.generateSasUrl({
      permissions: ContainerSASPermissions.from({ read: true, write: false, delete: false }),
      expiresOn: new Date(options?.expireTimestamp || Date.now() + 3600 * 1000),
      contentDisposition: options?.fileName ? `attachment; filename="${sanitize(options.fileName)}"` : undefined,
    });
    return [sasUrl];
  }

  public async createdUploadUrl(path: string, expires?: number): Promise<[string]> {
    const blockBlobClient = this.containerClient.getBlockBlobClient(path);
    const sasUrl = await blockBlobClient.generateSasUrl({
      permissions: ContainerSASPermissions.from({ read: true, write: true, delete: false }),
      expiresOn: new Date(expires || Date.now() + 15 * 60 * 1000),
      contentType: 'application/octet-stream',
    });
    return [sasUrl];
  }

  public async streamFileUpload({ path, contents }: StreamFileUploadParams): Promise<{ path: string }> {
    const blockBlobClient = this.containerClient.getBlockBlobClient(path);

    const passThroughStream = new PassThrough();
    passThroughStream.write(contents);
    passThroughStream.end();

    await blockBlobClient.uploadStream(passThroughStream);
    return { path };
  }

  public async streamBufferUpload({ path, contents }: StreamFileUploadParams<Buffer>) {
    const blockBlobClient = this.containerClient.getBlockBlobClient(path);
    await blockBlobClient.upload(contents, contents.length);
    return { path };
  }

  public async remove(path: string): Promise<{ path: string; statusCode: number }> {
    const blockBlobClient = this.containerClient.getBlockBlobClient(path);
    await blockBlobClient.delete();
    return {
      path,
      statusCode: 204,
    };
  }

  public getContentTypeFromMimeType(mimeType: string): string {
    const type = mime.contentType(mimeType);
    return typeof type === 'string' ? type : '';
  }

  public async copyFile({ source, destination, maxRetries = 3 }: CopyFileParams): Promise<{ path: string }> {
    if (!source || !destination || source === destination) {
      throw new ContextError('Source and destination paths must be provided and must not be the same.', {
        source,
        destination,
      });
    }

    const copyOperation = async () => {
      const srcBlobClient = this.containerClient.getBlobClient(source);
      const destBlobClient = this.containerClient.getBlobClient(destination);

      const srcProperties = await srcBlobClient.getProperties();
      if (!srcProperties) {
        throw new ContextError(`Source file does not exist.`, { source });
      }

      // Start the copy operation
      const copyPoller = await destBlobClient.beginCopyFromURL(srcBlobClient.url);
      await copyPoller.pollUntilDone();

      // Preserve original content type
      if (srcProperties.contentType) {
        await destBlobClient.setHTTPHeaders({ blobContentType: srcProperties.contentType });
      }

      return { path: destination };
    };

    return retryOperation(copyOperation, 0, maxRetries);
  }

  public async downloadFolder({ remotePath, localPath, replacePath = '' }: DownloadFolderParams) {
    // List blobs in the Azure Storage container with the given prefix
    const blobs = this.containerClient.listBlobsFlat({ prefix: remotePath });

    let totalSize = 0;
    const downloadedFiles = [];

    for await (const blob of blobs) {
      const fileName = path.basename(blob.name);
      const relativePath = replacePath ? blob.name.replace(replacePath, '').replace(fileName, '') : fileName;
      const destPath = path.join(localPath, relativePath);

      // Ensure directory exists
      mkDirByPathSync(destPath);

      const blobClient = this.containerClient.getBlobClient(blob.name);
      const downloadPath = path.join(destPath, fileName);

      // Download file from Azure Storage
      await blobClient.downloadToFile(downloadPath);

      // Get file size from blob properties
      const properties = await blobClient.getProperties();
      const fileSize = properties.contentLength || 0;
      totalSize += fileSize;

      downloadedFiles.push({ name: fileName, size: fileSize, path: downloadPath });
    }

    wwgLogger.info(`Downloaded ${downloadedFiles.length} files`, { downloadedFiles, totalSize });

    return { totalSize, files: downloadedFiles };
  }
}
