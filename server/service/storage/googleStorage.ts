/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import config from '../../config';
import { FileStorageInterface, DownloadFolderParams, StreamFileUploadParams, UploadFileParams, CopyFileParams, retryOperation } from './fileStorage';
import { Storage, Bucket, StorageOptions, FileMetadata } from '@google-cloud/storage';
import mime from 'mime-types';
import { PassThrough } from 'stream';
import { getGoogleCloudCredentials } from '../google-cloud/credentials';
import { wwgLogger } from '../wwgLogger';
import * as path from "path";
import fs from 'fs';
import { mkDirByPathSync } from '../file/filesystem';
import ContextError from '../../error/ContextError';
import sanitize from 'sanitize-filename';

export default class GoogleStorage implements FileStorageInterface {
  private readonly bucket: Bucket;
  private readonly baseUrl = config.googleCloud.storage.baseUrl;

  static create(bucketName = config.googleCloud.storage.bucket, options?: StorageOptions) {
    const storage = GoogleStorage.getBucket(options);
    wwgLogger.info(`Creating GoogleStorage connection to ${bucketName}`, {
      client_email: (options?.credentials as { client_email?: string })?.client_email,
      projectId: options?.projectId,
    });
    return new GoogleStorage(storage.bucket(bucketName));
  }

  constructor(bucket: Bucket) {
    this.bucket = bucket;
  }

  public async getChecks(): Promise<{ [key: string]: number | string }> {
    const [buckets] = await this.bucket.storage.getBuckets();
    const status: { [key: string]: number | string } = {
      type: 'Google',
      buckets: buckets.length,
    };
    buckets.forEach((bucket) => {
      status[bucket.name] = 'OK';
    });

    return status;
  }

  private static getBucket(options?: StorageOptions) {
    return new Storage(options ?? getGoogleCloudCredentials());
  }

  public async upload(source: string, uploadPath: string, contentType: string, isPublic = true) {
    const destination = this.getUploadPath(uploadPath);
    return this.bucket
      .upload(source, {
        public: isPublic,
        destination,
        contentType,
      })
      .then((result) => {
        const file: any = result[0];
        return {
          url: `${this.baseUrl}/${file.bucket.name}/${file.name}?${file.metadata.md5Hash}`,
          path: file.name,
        };
      });
  }

  public async uploadFile({ source, destination, contentType }: UploadFileParams) {
    if (!fs.existsSync(source)) {
      throw new ContextError(`File not found`, { source });
    }

    return this.bucket
      .upload(source, {
        public: false,
        destination,
        ...(contentType ? { contentType } : {}),
      })
      .then((result) => {
        const file = result[0];
        return {
          url: `${this.baseUrl}/${file.bucket.name}/${file.name}?${file.metadata.md5Hash}`,
          path: file.name,
        };
      });
  }

  public getPublicPath() {
    return `${this.baseUrl}/${this.bucket.name}`;
  }

  public getPublicUrl(filePath: string) {
    return `${this.getPublicPath()}/${this.getUploadPath(filePath)}`;
  }

  public getExtensionFromMimeType(mimeType: string): string {
    return String(mime.extension(mimeType));
  }

  public getUploadPath(destination: string) {
    if (typeof destination !== 'string') {
      throw new Error(`Expected destination to be type of string, received ${typeof destination}`);
    }

    const dirs = destination.split('/');
    const dest = dirs.pop();
    if (typeof dest !== 'string') {
      throw new Error(`Last Dir to be available`);
    }

    const chars = dest.split('');
    const count = chars.length > 3 ? 3 : chars.length;

    let prefix = '';
    for (let i = 0; i < count; i++) {
      prefix += dest.charAt(i) + '/';
    }

    const url = `${dirs.join('/')}/${prefix}${dest}`;
    return url.replace(/^\/+/g, '');
  }

  getDownloadUrl(path: string, fileName: string, options?: { pathType: 'exact' }): Promise<[string]> {
    const filePath = options && options.pathType === 'exact' ? path : this.getUploadPath(path);
    return this.bucket.file(filePath).getSignedUrl({
      promptSaveAs: fileName,
      action: 'read',
      expires: Date.now() + 3600 * 1000,
      responseDisposition: `attachment; filename="${fileName}"`,
    });
  }

  downloadFile(path: string, destination: string): Promise<[Buffer]> {
    return this.bucket.file(path).download({ destination });
  }

  getSignedUrl(path: string, options?: { expireTimestamp?: number; fileName?: string }): Promise<string[]> {
    return this.bucket.file(path).getSignedUrl({
      action: 'read',
      expires: options?.expireTimestamp || Date.now() + 3600 * 1000,
      ...(options?.fileName
        ? {
            promptSaveAs: sanitize(options.fileName),
            responseDisposition: `attachment; filename="${sanitize(options.fileName)}"`,
          }
        : {}),
    });
  }

  createdUploadUrl(path: string, expires?: number) {
    return this.bucket.file(path).getSignedUrl({
      action: 'write',
      version: 'v4',
      expires: expires || Date.now() + 15 * 60 * 1000, // 15 minutes,
      contentType: 'application/octet-stream',
    });
  }

  public async streamFileUpload({ path, contents }: StreamFileUploadParams): Promise<{ path: string }> {
    const file = this.bucket.file(path);

    const passThroughStream = new PassThrough();
    passThroughStream.write(contents);
    passThroughStream.end();

    return new Promise((resolve, reject) => {
      passThroughStream.pipe(file.createWriteStream()).on('finish', () => {
        // The file upload is complete
        resolve({ path });
      });

      passThroughStream.on('error', (error) => {
        reject(error);
      });
    });
  }

  public async streamBufferUpload({ path, contents }: StreamFileUploadParams<Buffer>) {
    const file = this.bucket.file(path);
    await file.save(contents);
    return { path };
  }

  public async remove(path: string): Promise<{ path: string; statusCode: number }> {
    const [response] = await this.bucket.file(path).delete();
    return {
      path,
      statusCode: response.statusCode,
    };
  }

  public getContentTypeFromMimeType(mimeType: string): string {
    const type = mime.contentType(mimeType);
    return typeof type === 'string' ? type : '';
  }

  public async copyFile({ source, destination, maxRetries = 3 }: CopyFileParams): Promise<{ path: string }> {
    if (!source || !destination || source === destination) {
      throw new ContextError('Source and destination paths must be provided and must not be the same.', {
        source,
        destination,
      });
    }

    const copyOperation = async () => {
      const srcFile = this.bucket.file(source);
      const destFile = this.bucket.file(destination);

      const [exists] = await srcFile.exists();
      if (!exists) {
        throw new ContextError(`Source file does not exist.`, { source });
      }

      // Start the copy operation
      const [copiedFile, metadata] = await srcFile.copy(destFile);

      // Preserve original content type
      if (metadata && typeof metadata === 'object' && 'contentType' in metadata) {
        await copiedFile.setMetadata({ contentType: (metadata as FileMetadata).contentType });
      }

      return { path: destination };
    };

    return retryOperation(copyOperation, 0, maxRetries);
  }

  public async downloadFolder({ remotePath, localPath, replacePath = '' }: DownloadFolderParams) {
    // List files in the Google Storage with the given prefix
    const [files] = await this.bucket.getFiles({ prefix: remotePath });
    let totalSize = 0;
    const downloadedFiles = [];

    for await (const file of files) {
      const fileName = path.basename(file.name);
      const relativePath = replacePath ? file.name.replace(replacePath, '').replace(fileName, '') : fileName;
      const destPath = path.join(localPath, relativePath);

      // Ensure directory exists
      mkDirByPathSync(destPath);

      const downloadPath = path.join(destPath, fileName);
      await file.download({ destination: downloadPath });

      // Get file size from metadata
      const fileSize = file.metadata.size ? parseInt(String(file.metadata.size), 10) : 0;
      totalSize += fileSize;

      downloadedFiles.push({ name: fileName, size: fileSize, path: downloadPath });
    }

    wwgLogger.info(`Downloaded ${downloadedFiles.length} files`, { downloadedFiles, totalSize });

    return { totalSize, files: downloadedFiles };
  }
}
