/*
 * Copyright (c) 2020. World Wide Generation Ltd
 */

import GoogleStorage from './googleStorage';
import documentModel, { DocumentType } from '../../models/document';
import { ObjectId } from 'bson';
import { wwgLogger } from '../wwgLogger';
import config from '../../config';
import { AzureStorage } from './azureStorage';

export interface StreamFileUploadParams<T = string> {
  path: string;
  contents: T;
}

export interface UploadFileParams {
  source: string;
  destination: string;
  contentType?: string;
}

export interface CopyFileParams {
  source: string;
  destination: string;
  maxRetries?: number;
}

export interface DownloadFolderParams {
  remotePath: string;
  localPath: string;
  replacePath?: string;
}

interface DownloadedFile {
  name: string;
  size: number;
}

export interface FileStorageInterface {
  upload(source: string, destination: string, contentType: string, isPublic: boolean): Promise<FileStorageResult>

  uploadFile(params: UploadFileParams): Promise<FileStorageResult>

  getPublicPath(): string

  getPublicUrl(filePath: string): string

  getExtensionFromMimeType(mimeType: string): string

  getContentTypeFromMimeType(mimeType: string): string

  getDownloadUrl(path: string, fileName: string, options?: { pathType: string }): Promise<[string]>

  downloadFile(path: string, destination: string): Promise<[Buffer]>

  getUploadPath(destination: string): string

  /** Create pre-signed url that is valid for 15min by default **/
  createdUploadUrl(path: string, expires?: number): Promise<[string]>

  streamFileUpload(uploadRequest: StreamFileUploadParams): Promise<{ path: string }>

  streamBufferUpload({ path, contents }: StreamFileUploadParams<Buffer>): Promise<{ path: string }>

  remove(uploadedFile: string): Promise<{ path: string; statusCode: number }>

  getSignedUrl(path: string, options?: { expireTimestamp?: number, fileName?: string }): Promise<string[]>

  getChecks(): Promise<{ [key: string]: number | string }>

  copyFile(params: CopyFileParams): Promise<{ path: string }>

  /**
   * @param remotePath: folder directory in cloud storage
   * @param localPath: destination directory in local
   * @param replacePath: remove a part of remote path to get a relative directory in local
   * Example:
   *  - remotePath: jobs/{jobId}/; remoteFilePath: jobs/{jobId}/{initiativeId}/file;
   *  - localPath: /tmp/{jobId}; replacePath: jobs/{jobId}
   *  -> localFilePath: /tmp/{jobId}/{initiativeId}/file
   */
  downloadFolder({ remotePath, localPath, replacePath }: DownloadFolderParams): Promise<{ totalSize: number; files: DownloadedFile[] }>
}

export interface FileStorageResult {
  url: string;
  path: string;
}

interface DocAggregation {
  documents: MinDocData[];
}

type Metadata = {
  name: string;
  extension: string;
};

interface MinDocData {
  _id?: any;
  url?: string;
  type: DocumentType;
  path: string;
  public?: boolean
  metadata?: Metadata;
  title?: string;
}

interface DocDataWithMeta extends MinDocData {
  metadata: Metadata
}

const hasMetadata = (doc: MinDocData): doc is DocDataWithMeta => doc.metadata !== undefined;

let fileStorage: FileStorageInterface;
export const getStorage = (): FileStorageInterface => {
  if (!fileStorage) {
    fileStorage = config.cloudEnv === "azure" ? AzureStorage.create() : GoogleStorage.create()
  }
  return fileStorage;
}

export const addFileUrl = (item: any) => {

  if (item.title === 'undefined') {
    item.title = '';
  }
  if (item.description === 'undefined') {
    item.description = '';
  }

  const filePath = `${item._id}.${item.metadata.extension}`;
  item.url = getStorage().getPublicUrl(filePath);
  return item;
};

const getPath = (doc: MinDocData) => {
  if (doc.path) {
    return doc.path;
  }

  if (!hasMetadata(doc)) {
    throw new Error(`Document ${doc._id} is missing metadata, required for getPath`)
  }
  return getStorage().getUploadPath(`${doc._id}.${doc.metadata.extension}`);
};

const createUrl = async (doc: MinDocData) => {

  if (doc.type === DocumentType.Link) {
    doc.url = doc.path;
    return doc;
  }

  if (doc.public === false) {
    // Resolve path
    const path = getPath(doc);
    doc.url = await getStorage().getSignedUrl(path, { fileName: doc.title ?? doc.metadata?.name })
      .then(([url]) => url)
      .catch((e) => {
        wwgLogger.error(e);
        return undefined
      });
    return doc;
  }

  if (!doc.path) {
    return addFileUrl(doc);
  }

  doc.url = `${getStorage().getPublicPath()}/${doc.path}`;
  return doc;
};

const addUrl = async (d: DocAggregation) => {
  if (d && Array.isArray(d.documents)) {
    for (const doc of d.documents) {
      await createUrl(doc);
    }
  }
};

export const addDataPropertyUrl = async (d: any, propertyName: string) => {
  if (d && Array.isArray(d[propertyName])) {
    for (const doc of d[propertyName]) {
      await createUrl(doc);
    }
  }
};

export const addDocumentUrl = async (data: any) => {
  if (Array.isArray(data)) {
    for (const d of data) {
      await addUrl(d);
    }
    return data;
  }

  await addUrl(data);
  return data;
};


export const createDownloadUrl = async <T extends MinDocData>(doc: T): Promise<T> => {
  if (doc.type === DocumentType.Link) {
    doc.url = doc.path;
    return doc;
  }

  if (!hasMetadata(doc)) {
    throw new Error(`Document ${doc._id} is missing metadata, required for download url`)
  }

  const [url] = await getStorage().getDownloadUrl(
    doc.path,
    doc.metadata.name,
    { pathType: 'exact' }
  );
  doc.url = url;
  return doc;
};


export const addDocumentDownloadUrl = async (data: any) => {
  if (Array.isArray(data)) {
    for (const d of data) {
      if (d && Array.isArray(d.documents)) {
        for (const doc of d.documents) {
          await createDownloadUrl(doc);
        }
      }
    }
    return data;
  }

  if (data && Array.isArray(data.documents)) {
    for (const doc of data.documents) {
      await createDownloadUrl(doc);
    }
  }
  return data;
};

export const loadDocumentDownloadUrls = async (ids: ObjectId[]) => {
  const docs = await documentModel.find({ _id: { $in: ids } }).lean().exec();
  const { documents } = await addDocumentDownloadUrl({ documents: docs });
  return documents;
};

export const loadDocuments = async (ids: ObjectId[]) => {
  const docs = await documentModel.find({ _id: { $in: ids } }).lean().exec();
  const { documents } = await addDocumentUrl({ documents: docs });
  return documents;
};

export const retryOperation = async <T>(operation: () => Promise<T>, retryCount = 0, maxRetries = 3): Promise<T> => {
  try {
    return await operation();
  } catch (error) {
    wwgLogger.error('Error during operation:', error);

    if (
      (error.statusCode === 409 ||
        error.message.includes('conflict') ||
        error.message.includes('metadata was edited during the operation')) &&
      retryCount < maxRetries
    ) {
      const delay = Math.pow(2, retryCount) * 100;
      wwgLogger.info(`Retrying operation... Attempt ${retryCount + 1}/${maxRetries} after ${delay}ms`);

      await new Promise((resolve) => setTimeout(resolve, delay));

      return retryOperation(operation, retryCount + 1, maxRetries);
    } else if (retryCount >= maxRetries) {
      wwgLogger.error(`Max retries reached (${maxRetries}). Failing operation.`);
    }

    throw error;
  }
};
