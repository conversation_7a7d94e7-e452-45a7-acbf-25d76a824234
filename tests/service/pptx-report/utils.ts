/*
 * Copyright (c) 2023. World Wide Generation Ltd
 */

import { ObjectId } from 'bson';
import { HelperUtr, HelperUtrv } from '../../../server/service/pptx-report/templates/PPTXTemplateSurveyCache';
import { UtrValueType } from '../../../server/models/public/universalTrackerType';
import { ActionList } from '../../../server/service/utr/constants';

const anyId = new ObjectId();
export const createUtrv: (overrides?: Partial<HelperUtrv<Partial<HelperUtr>>>) => HelperUtrv = (overrides) => ({
  _id: anyId,
  valueType: UtrValueType.Date,
  universalTrackerId: anyId,
  value: undefined,
  valueData: undefined,
  notes: undefined,
  note: undefined,
  status: ActionList.Created,
  ...overrides,
  universalTracker: {
    _id: anyId,
    name: 'Question One',
    valueLabel: 'Question One',
    code: 'any/code',
    valueType: UtrValueType.Date,
    type: 'any/type',
    ...overrides?.universalTracker
  },
});
