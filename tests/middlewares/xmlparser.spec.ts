
import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import { Readable } from 'stream';
import { xmlParserHandler } from '../../server/routes/inbound';

describe('XML Parser Middleware', () => {
  let res: Partial<Response>;
  let next: sinon.SinonSpy;

  beforeEach(() => {
    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub(),
    };
    next = sinon.spy();
  });

  it('should parse the xml body and attach it to req.body', (done) => {
    const xml = `
      <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
        <soapenv:Body>
          <notifications xmlns="http://soap.sforce.com/2005/09/outbound">
            <OrganizationId>some-org-id</OrganizationId>
          </notifications>
        </soapenv:Body>
      </soapenv:Envelope>
    `;

    const reqStream = new Readable();
    reqStream.push(xml);
    reqStream.push(null);
    (reqStream as any).headers = { 'content-type': 'text/xml' };

    xmlParserHandler(reqStream as Request, res as Response, (err?: any) => {
      expect(err).to.be.undefined;
      const body = (reqStream as any).body;
      expect(body).to.deep.equal({
        'soapenv:Envelope': {
          'soapenv:Body': {
            notifications: {
              OrganizationId: 'some-org-id'
            }
          }
        }
      });
      done();
    });
  });

  it('should call next with an error for invalid XML', (done) => {
    const xml = `<invalid-xml`;

    const reqStream = new Readable();
    reqStream.push(xml);
    reqStream.push(null);
    (reqStream as any).headers = { 'content-type': 'text/xml' };

    xmlParserHandler(reqStream as Request, res as Response, (err?: any) => {
      expect(err).to.not.be.undefined;
      done();
    });
  });
});
