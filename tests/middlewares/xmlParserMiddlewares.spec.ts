import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import { xmlParserHandler } from '../../server/middleware/xmlParserMiddlewares';

describe('XML Parser Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let nextFunction: sinon.SinonSpy;
  let sandbox: sinon.SinonSandbox;



  // Helper functions
  const createMockRequest = (contentType = 'text/xml', body?: any): Partial<Request> => ({
    headers: {
      'content-type': contentType,
    },
    body,
    complete: false,
  });

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    mockResponse = {
      status: sandbox.stub().returnsThis(),
      json: sandbox.stub(),
    };
    nextFunction = sandbox.spy();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should be configured with correct XML parser options', () => {
    // Test that the middleware is properly configured
    expect(xmlParserHandler).to.be.a('function');

    // The middleware should be an instance of express-xml-bodyparser with specific options
    // We can verify this by checking that it's a function (middleware signature)
    expect(xmlParserHandler.length).to.equal(3); // req, res, next parameters
  });

  it('should skip parsing for non-XML content types', () => {
    mockRequest = createMockRequest('application/json', { test: 'data' });

    xmlParserHandler(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(nextFunction.calledOnce).to.be.true;
    expect(nextFunction.args[0][0]).to.be.undefined;
    expect(mockRequest.body).to.deep.equal({ test: 'data' });
  });

  it('should handle XML content type requests', () => {
    mockRequest = createMockRequest('text/xml');

    xmlParserHandler(mockRequest as Request, mockResponse as Response, nextFunction);

    // The middleware should call next() for XML content types when no body is present
    expect(nextFunction.calledOnce).to.be.true;
    expect(nextFunction.args[0][0]).to.be.undefined; // No error passed to next
  });

  it('should handle application/xml content type requests', () => {
    mockRequest = createMockRequest('application/xml');

    xmlParserHandler(mockRequest as Request, mockResponse as Response, nextFunction);

    // Similar to text/xml, should call next() when no body is present
    expect(nextFunction.calledOnce).to.be.true;
    expect(nextFunction.args[0][0]).to.be.undefined; // No error passed to next
  });
});
