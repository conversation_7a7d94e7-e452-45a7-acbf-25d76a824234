import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import { EventEmitter } from 'events';
import { xmlParserHandler } from '../../server/middleware/xmlParserMiddlewares';

describe('XML Parser Middleware', () => {
  let mockRequest: Partial<Request> & EventEmitter;
  let mockResponse: Partial<Response>;
  let nextFunction: sinon.SinonSpy;
  let sandbox: sinon.SinonSandbox;

  beforeEach(() => {
    sandbox = sinon.createSandbox();
    mockRequest = Object.assign(new EventEmitter(), {
      headers: {
        'content-type': 'text/xml',
        'content-length': '100',
      },
      body: undefined,
      readable: true,
      readableEnded: false,
      _body: false,
      complete: false,
      setEncoding: sandbox.stub(),
    });
    mockResponse = {
      status: sandbox.stub().returnsThis(),
      json: sandbox.stub(),
    };
    nextFunction = sandbox.spy();
  });

  afterEach(() => {
    sandbox.restore();
  });

  it('should parse Salesforce customer journey XML notification and attach it to req.body', (done) => {
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope
 xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
 xmlns:xsd="http://www.w3.org/2001/XMLSchema"
 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
 <soapenv:Body>
 <notifications
 xmlns="http://soap.sforce.com/2005/09/outbound">
 <OrganizationId>00D3z000001BYGGEA4</OrganizationId>
 <ActionId>04k3z000000GxOzAAK</ActionId>
 <SessionId xsi:nil="true"/>
 <EnterpriseUrl>https://worldwidegeneration.my.salesforce.com/services/Soap/c/52.0/00D3z000001BYGG</EnterpriseUrl>
 <PartnerUrl>https://worldwidegeneration.my.salesforce.com/services/Soap/u/52.0/00D3z000001BYGG</PartnerUrl>
 <Notification>
 <Id>04l3z00001a8PT1AAM</Id>
 <sObject xsi:type="sf:Account"
 xmlns:sf="urn:sobject.enterprise.soap.sforce.com">
 <sf:Id>0013z00002PUj2qAAD</sf:Id>
 <sf:Account_Status__c>Active</sf:Account_Status__c>
 <sf:Customer_Journey__c>Pipeline</sf:Customer_Journey__c>
 <sf:G17Eco_ID__c>5babc4e326140135c24b5ad0</sf:G17Eco_ID__c>
 </sObject>
 </Notification>
 </notifications>
 </soapenv:Body>
 </soapenv:Envelope>`;

    mockRequest.headers = {
      'content-type': 'text/xml',
      'content-length': xml.length.toString(),
    };

    xmlParserHandler(mockRequest as Request, mockResponse as Response, (err?: any) => {
      try {
        expect(err).to.be.undefined;
        expect(mockRequest.body).to.deep.equal({
          'soapenv:Envelope': {
            'soapenv:Body': {
              notifications: {
                OrganizationId: '00D3z000001BYGGEA4',
                ActionId: '04k3z000000GxOzAAK',
                SessionId: '',
                EnterpriseUrl: 'https://worldwidegeneration.my.salesforce.com/services/Soap/c/52.0/00D3z000001BYGG',
                PartnerUrl: 'https://worldwidegeneration.my.salesforce.com/services/Soap/u/52.0/00D3z000001BYGG',
                Notification: {
                  Id: '04l3z00001a8PT1AAM',
                  sObject: {
                    'sf:Id': '0013z00002PUj2qAAD',
                    'sf:Account_Status__c': 'Active',
                    'sf:Customer_Journey__c': 'Pipeline',
                    'sf:G17Eco_ID__c': '5babc4e326140135c24b5ad0',
                  },
                },
              },
            },
          },
        });
        done();
      } catch (error) {
        done(error);
      }
    });

    // Simulate incoming data immediately
    process.nextTick(() => {
      mockRequest.emit('data', xml);
      mockRequest.emit('end');
    });
  });

  it('should call next with an error for invalid XML', (done) => {
    const xml = `<invalid-xml`;

    mockRequest.headers = {
      'content-type': 'text/xml',
      'content-length': xml.length.toString(),
    };

    xmlParserHandler(mockRequest as Request, mockResponse as Response, (err?: any) => {
      try {
        expect(err).to.not.be.undefined;
        expect(err.message).to.match(/Unexpected end|Non-whitespace before first tag|Unclosed root tag/);
        done();
      } catch (error) {
        done(error);
      }
    });

    // Simulate incoming invalid data immediately
    process.nextTick(() => {
      mockRequest.emit('data', xml);
      mockRequest.emit('end');
    });
  });

  it('should skip parsing for non-XML content types', () => {
    mockRequest.headers = { 'content-type': 'application/json' };
    mockRequest.body = { test: 'data' };

    xmlParserHandler(mockRequest as Request, mockResponse as Response, nextFunction);

    expect(nextFunction.calledOnce).to.be.true;
    expect(nextFunction.args[0][0]).to.be.undefined;
    expect(mockRequest.body).to.deep.equal({ test: 'data' });
  });

  it('should parse customer journey change notification with all Account fields', (done) => {
    const xml = `<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
 <soapenv:Body>
 <notifications xmlns="http://soap.sforce.com/2005/09/outbound">
 <OrganizationId>00D3z000001BYGGEA4</OrganizationId>
 <Notification>
 <Id>04l3z00001a8PT1AAM</Id>
 <sObject xsi:type="sf:Account" xmlns:sf="urn:sobject.enterprise.soap.sforce.com" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
 <sf:Id>0013z00002PUj2qAAD</sf:Id>
 <sf:Name>Test Company</sf:Name>
 <sf:AccountNumber>ACC-001</sf:AccountNumber>
 <sf:Customer_Journey__c>Active Customer</sf:Customer_Journey__c>
 <sf:Account_Status__c>Active</sf:Account_Status__c>
 <sf:Company_Registration__c>REG123456</sf:Company_Registration__c>
 <sf:Contract_type__c>Premium</sf:Contract_type__c>
 <sf:G17Eco_ID__c>5babc4e326140135c24b5ad0</sf:G17Eco_ID__c>
 <sf:Industry>Technology</sf:Industry>
 </sObject>
 </Notification>
 </notifications>
 </soapenv:Body>
 </soapenv:Envelope>`;

    mockRequest.headers = {
      'content-type': 'text/xml',
      'content-length': xml.length.toString(),
    };

    xmlParserHandler(mockRequest as Request, mockResponse as Response, (err?: any) => {
      try {
        expect(err).to.be.undefined;

        const notification = mockRequest.body['soapenv:Envelope']['soapenv:Body'].notifications.Notification;
        const account = notification.sObject;

        // Verify all Account fields are parsed correctly
        expect(account['sf:Id']).to.equal('0013z00002PUj2qAAD');
        expect(account['sf:Name']).to.equal('Test Company');
        expect(account['sf:AccountNumber']).to.equal('ACC-001');
        expect(account['sf:Customer_Journey__c']).to.equal('Active Customer');
        expect(account['sf:Account_Status__c']).to.equal('Active');
        expect(account['sf:Company_Registration__c']).to.equal('REG123456');
        expect(account['sf:Contract_type__c']).to.equal('Premium');
        expect(account['sf:G17Eco_ID__c']).to.equal('5babc4e326140135c24b5ad0');
        expect(account['sf:Industry']).to.equal('Technology');

        done();
      } catch (error) {
        done(error);
      }
    });

    // Simulate incoming data
    setTimeout(() => {
      mockRequest.emit('data', xml);
      mockRequest.emit('end');
    }, 10);
  });
});
