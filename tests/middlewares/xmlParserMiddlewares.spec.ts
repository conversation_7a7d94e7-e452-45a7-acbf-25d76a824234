
import { expect } from 'chai';
import sinon from 'sinon';
import { Request, Response } from 'express';
import { EventEmitter } from 'events';
import { xmlParserHandler } from '../../server/middleware/xmlParserMiddlewares';

describe('XML Parser Middleware', () => {
  let req: Partial<Request> & EventEmitter;
  let res: Partial<Response>;
  let next: sinon.SinonSpy;

  beforeEach(() => {
    req = Object.assign(new EventEmitter(), {
      headers: {
        'content-type': 'text/xml',
        'content-length': '100'
      },
      body: undefined,
      readable: true,
      readableEnded: false,
      _body: false,
      complete: false,
      setEncoding: sinon.stub(),
    });
    res = {
      status: sinon.stub().returnsThis(),
      json: sinon.stub(),
    };
    next = sinon.spy();

  });

  it('should parse the xml body and attach it to req.body', (done) => {
    const xml = `<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
        <soapenv:Body>
          <notifications xmlns="http://soap.sforce.com/2005/09/outbound">
            <OrganizationId>some-org-id</OrganizationId>
          </notifications>
        </soapenv:Body>
      </soapenv:Envelope>`;

    req.headers = {
      'content-type': 'text/xml',
      'content-length': xml.length.toString()
    };

    xmlParserHandler(req as Request, res as Response, (err?: any) => {
      try {
        expect(err).to.be.undefined;
        expect(req.body).to.deep.equal({
          'soapenv:Envelope': {
            'soapenv:Body': {
              notifications: {
                OrganizationId: 'some-org-id'
              }
            }
          }
        });
        done();
      } catch (error) {
        done(error);
      }
    });

    // Simulate incoming data
    setTimeout(() => {
      req.emit('data', xml);
      req.emit('end');
    }, 10);
  });

  it('should call next with an error for invalid XML', (done) => {
    const xml = `<invalid-xml`;

    req.headers = {
      'content-type': 'text/xml',
      'content-length': xml.length.toString()
    };

    xmlParserHandler(req as Request, res as Response, (err?: any) => {
      try {
        expect(err).to.not.be.undefined;
        expect(err.message).to.match(/Unexpected end|Non-whitespace before first tag|Unclosed root tag/);
        done();
      } catch (error) {
        done(error);
      }
    });

    // Simulate incoming invalid data
    setTimeout(() => {
      req.emit('data', xml);
      req.emit('end');
    }, 10);
  });

  it('should skip parsing for non-XML content types', (done) => {
    req.headers = { 'content-type': 'application/json' };
    req.body = { test: 'data' };

    xmlParserHandler(req as Request, res as Response, (err?: any) => {
      try {
        expect(err).to.be.undefined;
        expect(req.body).to.deep.equal({ test: 'data' });
        done();
      } catch (error) {
        done(error);
      }
    });
  });
});
